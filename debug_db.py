#!/usr/bin/env python3
"""
Debug script to check MongoDB connection and user data
"""

import os
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

# Get connection details
MONGODB_URI = os.getenv('MONGODB_URI')
DB_NAME = os.getenv('DB_NAME', 'moneytracker')

print(f"Connecting to MongoDB...")
print(f"Database name: {DB_NAME}")
print(f"URI: {MONGODB_URI[:50]}...")

try:
    # Connect to MongoDB
    client = MongoClient(MONGODB_URI)
    db = client[DB_NAME]
    
    # Test connection
    client.admin.command('ping')
    print("✅ Successfully connected to MongoDB!")
    
    # List all databases
    print(f"\n📋 Available databases:")
    for db_info in client.list_databases():
        print(f"  - {db_info['name']}")
    
    # Check collections in our database
    print(f"\n📋 Collections in '{DB_NAME}' database:")
    collections = db.list_collection_names()
    for collection in collections:
        count = db[collection].count_documents({})
        print(f"  - {collection}: {count} documents")
    
    # Check users collection specifically
    if 'users' in collections:
        print(f"\n👥 Users in the database:")
        users = db.users.find({})
        for user in users:
            print(f"  - ID: {user['_id']}")
            print(f"    Username: {user.get('username', 'N/A')}")
            print(f"    Email: {user.get('email', 'N/A')}")
            print(f"    Has password: {'password' in user}")
            if 'password' in user:
                print(f"    Password starts with: {user['password'][:20]}...")
            print(f"    Registered: {user.get('registered_on', 'N/A')}")
            print(f"    Last login: {user.get('last_login', 'N/A')}")
            print()
    else:
        print(f"\n❌ No 'users' collection found in '{DB_NAME}' database")
    
    # Check if there are users in other databases
    print(f"\n🔍 Checking for users in other databases...")
    for db_info in client.list_databases():
        if db_info['name'] not in ['admin', 'local', 'config']:
            other_db = client[db_info['name']]
            if 'users' in other_db.list_collection_names():
                user_count = other_db.users.count_documents({})
                if user_count > 0:
                    print(f"  - Found {user_count} users in '{db_info['name']}' database")
                    # Show first few users
                    users = other_db.users.find({}, {'username': 1, 'email': 1}).limit(3)
                    for user in users:
                        print(f"    * {user.get('username', 'N/A')} ({user.get('email', 'N/A')})")

except Exception as e:
    print(f"❌ Error connecting to MongoDB: {e}")
finally:
    if 'client' in locals():
        client.close()

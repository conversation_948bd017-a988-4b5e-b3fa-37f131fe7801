# Git
.git
.gitignore
README.md
LICENSE

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Environment files
.env
.env.local
.env.*.local

# Logs
logs/
*.log

# Documentation
wiki/
docs/

# Development files
versions.md
render.yaml

# SSL certificates (if any)
ssl/
*.pem
*.key
*.crt

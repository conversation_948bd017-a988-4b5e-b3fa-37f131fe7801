# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your-very-secure-secret-key-change-this-in-production

# Database Configuration (for Docker Compose)
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-secure-password-here
DB_NAME=moneytracker
MONGODB_URI=*************************************************************************************

# Alternative: External MongoDB (if not using Docker Compose MongoDB)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/moneytracker?retryWrites=true&w=majority

# Email Configuration (for password reset functionality)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
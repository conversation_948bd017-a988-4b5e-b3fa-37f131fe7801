#!/bin/bash

# Money Tracker Docker Helper Script
# This script provides convenient commands for managing the Docker deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if Docker and Docker Compose are installed
check_requirements() {
    print_header "Checking Requirements"

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Check for Docker Compose (plugin version first, then standalone)
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        print_status "Docker and Docker Compose (plugin) are installed"
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        print_status "Docker and Docker Compose (standalone) are installed"
    else
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
}

# Setup environment file
setup_env() {
    print_header "Setting up Environment"
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_status "Created .env file from .env.example"
            print_warning "Please edit .env file with your configuration before starting the application"
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_status ".env file already exists"
    fi
}

# Start services
start_services() {
    print_header "Starting Services"

    if [ "$1" = "production" ]; then
        print_status "Starting in production mode (with Nginx)"
        $DOCKER_COMPOSE_CMD up -d
    else
        print_status "Starting in development mode (without Nginx)"
        $DOCKER_COMPOSE_CMD up -d app mongodb
    fi

    print_status "Services started successfully"
    print_status "Application will be available at:"
    if [ "$1" = "production" ]; then
        echo "  - HTTP: http://localhost"
        echo "  - HTTPS: https://localhost (if SSL configured)"
    fi
    echo "  - Direct: http://localhost:5000"
}

# Stop services
stop_services() {
    print_header "Stopping Services"
    $DOCKER_COMPOSE_CMD down
    print_status "Services stopped successfully"
}

# View logs
view_logs() {
    print_header "Viewing Logs"
    if [ -n "$1" ]; then
        $DOCKER_COMPOSE_CMD logs -f "$1"
    else
        $DOCKER_COMPOSE_CMD logs -f
    fi
}

# Check status
check_status() {
    print_header "Service Status"
    $DOCKER_COMPOSE_CMD ps

    print_header "Health Checks"

    # Check app health
    if curl -f http://localhost:5000/health &> /dev/null; then
        print_status "Application is healthy"
    else
        print_warning "Application health check failed"
    fi

    # Check MongoDB
    if $DOCKER_COMPOSE_CMD exec -T mongodb mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
        print_status "MongoDB is healthy"
    else
        print_warning "MongoDB health check failed"
    fi
}

# Backup database
backup_database() {
    print_header "Creating Database Backup"

    BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"

    $DOCKER_COMPOSE_CMD exec -T mongodb mongodump --db moneytracker --out /tmp/backup
    docker cp moneytracker-mongodb:/tmp/backup/moneytracker "$BACKUP_DIR/"

    print_status "Database backup created at: $BACKUP_DIR"
}

# Restore database
restore_database() {
    if [ -z "$1" ]; then
        print_error "Please provide backup directory path"
        print_status "Usage: $0 restore <backup_directory>"
        exit 1
    fi

    if [ ! -d "$1" ]; then
        print_error "Backup directory not found: $1"
        exit 1
    fi

    print_header "Restoring Database"
    print_warning "This will overwrite the current database. Continue? (y/N)"
    read -r response

    if [[ "$response" =~ ^[Yy]$ ]]; then
        docker cp "$1" moneytracker-mongodb:/tmp/restore
        $DOCKER_COMPOSE_CMD exec -T mongodb mongorestore --db moneytracker --drop /tmp/restore
        print_status "Database restored successfully"
    else
        print_status "Database restore cancelled"
    fi
}

# Update application
update_app() {
    print_header "Updating Application"
    
    print_status "Pulling latest changes..."
    git pull
    
    print_status "Rebuilding and restarting services..."
    docker-compose down
    docker-compose up -d --build
    
    print_status "Application updated successfully"
}

# Clean up
cleanup() {
    print_header "Cleaning Up"
    print_warning "This will remove all containers, networks, and volumes. Continue? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        docker-compose down -v --rmi all
        print_status "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Show help
show_help() {
    echo "Money Tracker Docker Helper"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup              Setup environment file"
    echo "  start [production] Start services (add 'production' for full stack with Nginx)"
    echo "  stop               Stop all services"
    echo "  restart            Restart all services"
    echo "  status             Show service status and health"
    echo "  logs [service]     View logs (optionally for specific service)"
    echo "  backup             Create database backup"
    echo "  restore <dir>      Restore database from backup directory"
    echo "  update             Update application (git pull + rebuild)"
    echo "  cleanup            Remove all containers, networks, and volumes"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup           # Setup environment file"
    echo "  $0 start           # Start in development mode"
    echo "  $0 start production # Start with Nginx"
    echo "  $0 logs app        # View application logs"
    echo "  $0 backup          # Create database backup"
}

# Main script logic
case "$1" in
    setup)
        check_requirements
        setup_env
        ;;
    start)
        check_requirements
        start_services "$2"
        ;;
    stop)
        stop_services
        ;;
    restart)
        stop_services
        start_services "$2"
        ;;
    status)
        check_status
        ;;
    logs)
        view_logs "$2"
        ;;
    backup)
        backup_database
        ;;
    restore)
        restore_database "$2"
        ;;
    update)
        update_app
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

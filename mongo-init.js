// MongoDB initialization script for Money Tracker
// This script runs when the MongoDB container starts for the first time

// Switch to the moneytracker database
db = db.getSiblingDB('moneytracker');

// Create collections with validation schemas
db.createCollection("users", {
   validator: {
      $jsonSchema: {
         bsonType: "object",
         required: ["username", "email", "password"],
         properties: {
            username: {
               bsonType: "string",
               description: "must be a string and is required"
            },
            email: {
               bsonType: "string",
               pattern: "^.+@.+$",
               description: "must be a valid email address and is required"
            },
            password: {
               bsonType: "string",
               description: "must be a string and is required"
            },
            registered_on: {
               bsonType: "date",
               description: "must be a date"
            },
            last_login: {
               bsonType: "date",
               description: "must be a date"
            }
         }
      }
   }
});

db.createCollection("expenses", {
   validator: {
      $jsonSchema: {
         bsonType: "object",
         required: ["user_id", "amount", "date", "transaction_type"],
         properties: {
            user_id: {
               bsonType: "string",
               description: "must be a string and is required"
            },
            amount: {
               bsonType: "number",
               minimum: 0,
               description: "must be a positive number and is required"
            },
            date: {
               bsonType: "date",
               description: "must be a date and is required"
            },
            transaction_type: {
               enum: ["CR", "DR"],
               description: "must be either CR or DR and is required"
            },
            category_id: {
               bsonType: "string",
               description: "must be a string"
            },
            description: {
               bsonType: "string",
               description: "must be a string"
            }
         }
      }
   }
});

db.createCollection("salaries");
db.createCollection("categories");
db.createCollection("budgets");

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.expenses.createIndex({ "user_id": 1, "date": -1 });
db.expenses.createIndex({ "user_id": 1, "category_id": 1 });
db.salaries.createIndex({ "user_id": 1, "date": -1 });
db.categories.createIndex({ "user_id": 1 });
db.budgets.createIndex({ "user_id": 1 });

// Insert default global categories
db.categories.insertMany([
   {
      name: "Food & Dining",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Transportation",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Shopping",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Entertainment",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Bills & Utilities",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Healthcare",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Education",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Travel",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Personal Care",
      user_id: null,
      is_global: true,
      created_at: new Date()
   },
   {
      name: "Other",
      user_id: null,
      is_global: true,
      created_at: new Date()
   }
]);

print("MongoDB initialization completed successfully!");
print("Created collections: users, expenses, salaries, categories, budgets");
print("Created indexes for better performance");
print("Inserted default global categories");

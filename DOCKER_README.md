# Money Tracker - Docker Deployment Guide

This guide explains how to deploy the Money Tracker application using Docker and Docker Compose.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)
- At least 2GB of available RAM
- At least 5GB of available disk space

## Quick Start

1. **Clone the repository** (if you haven't already):
   ```bash
   git clone <your-repo-url>
   cd moneytracker
   ```

2. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

3. **Edit the `.env` file** with your configuration:
   ```bash
   nano .env
   ```
   
   **Important**: Change the following values:
   - `MONGO_ROOT_PASSWORD`: Use a strong password
   - `SECRET_KEY`: Generate a secure secret key
   - `MAIL_USERNAME` and `MAIL_PASSWORD`: Your email credentials (for password reset)

4. **Start the application**:
   ```bash
   docker-compose up -d
   ```

5. **Access the application**:
   - HTTP: http://localhost:5000
   - With Nginx (if enabled): http://localhost

## Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MONGO_ROOT_USERNAME` | MongoDB root username | `admin` | Yes |
| `MONGO_ROOT_PASSWORD` | MongoDB root password | `password123` | Yes |
| `DB_NAME` | Database name | `moneytracker` | Yes |
| `SECRET_KEY` | Flask secret key | - | Yes |
| `MAIL_SERVER` | SMTP server | `smtp.gmail.com` | No |
| `MAIL_PORT` | SMTP port | `587` | No |
| `MAIL_USERNAME` | Email username | - | No |
| `MAIL_PASSWORD` | Email password | - | No |

### Services

The Docker Compose setup includes:

1. **MongoDB** (`mongodb`):
   - Port: 27017
   - Data persistence via Docker volume
   - Health checks enabled

2. **Money Tracker App** (`app`):
   - Port: 5000
   - Built from local Dockerfile
   - Health checks enabled
   - Depends on MongoDB

3. **Nginx** (`nginx`) - Optional:
   - Ports: 80, 443
   - Reverse proxy with SSL support
   - Rate limiting and security headers

## Deployment Options

### Option 1: Basic Deployment (App + Database)

```bash
# Start only the app and database
docker-compose up -d app mongodb
```

### Option 2: Full Deployment (App + Database + Nginx)

```bash
# Start all services including Nginx
docker-compose up -d
```

### Option 3: Production Deployment

For production, you should:

1. **Use external MongoDB** (recommended):
   ```bash
   # Update .env file
   MONGODB_URI=mongodb+srv://username:<EMAIL>/moneytracker?retryWrites=true&w=majority
   
   # Start only the app
   docker-compose up -d app
   ```

2. **Configure SSL certificates** for Nginx:
   ```bash
   # Create SSL directory
   mkdir ssl
   
   # Add your certificates
   cp your-cert.pem ssl/cert.pem
   cp your-key.pem ssl/key.pem
   
   # Update nginx.conf to uncomment SSL lines
   ```

## Management Commands

### View logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app
docker-compose logs -f mongodb
```

### Stop services
```bash
docker-compose down
```

### Restart services
```bash
docker-compose restart
```

### Update application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose up -d --build
```

### Database backup
```bash
# Create backup
docker-compose exec mongodb mongodump --db moneytracker --out /data/backup

# Copy backup to host
docker cp moneytracker-mongodb:/data/backup ./backup
```

### Database restore
```bash
# Copy backup to container
docker cp ./backup moneytracker-mongodb:/data/backup

# Restore database
docker-compose exec mongodb mongorestore --db moneytracker /data/backup/moneytracker
```

## Monitoring and Health Checks

### Health Check Endpoints

- Application: `http://localhost:5000/health`
- MongoDB: Built-in Docker health check

### View container status
```bash
docker-compose ps
```

### Monitor resource usage
```bash
docker stats
```

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   # Check what's using the port
   sudo lsof -i :5000
   
   # Change port in docker-compose.yml
   ports:
     - "8080:5000"  # Use port 8080 instead
   ```

2. **Database connection failed**:
   ```bash
   # Check MongoDB logs
   docker-compose logs mongodb
   
   # Verify environment variables
   docker-compose exec app env | grep MONGO
   ```

3. **Permission denied errors**:
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Reset Everything

```bash
# Stop and remove all containers, networks, and volumes
docker-compose down -v

# Remove all images
docker-compose down --rmi all

# Start fresh
docker-compose up -d --build
```

## Security Considerations

1. **Change default passwords** in `.env` file
2. **Use strong secret keys**
3. **Enable SSL/TLS** in production
4. **Regularly update** Docker images
5. **Monitor logs** for suspicious activity
6. **Backup data** regularly

## Performance Tuning

### For Production

1. **Increase worker processes**:
   ```dockerfile
   CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "8", "--timeout", "120", "app:app"]
   ```

2. **Add Redis for caching** (optional):
   ```yaml
   redis:
     image: redis:alpine
     ports:
       - "6379:6379"
   ```

3. **Use external database** for better performance
4. **Configure log rotation**
5. **Monitor resource usage**

## Support

If you encounter issues:

1. Check the logs: `docker-compose logs -f`
2. Verify your `.env` configuration
3. Ensure all required ports are available
4. Check Docker and Docker Compose versions

For more help, please refer to the main README.md or create an issue in the repository.
